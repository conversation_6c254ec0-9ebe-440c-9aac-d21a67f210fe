export type ObterInformacoesProdutosResponse = {
  ratearIcmsSt: boolean;
};

export type InformacoesRodape = {
  totalProdutos: number;
  quantidadeItens: number;
  valorTotalProdutos: number;
};

export type Produto = {
  isOpen: boolean;
  entradaMercadoriaItemId: string;
  nomeProduto: string;
  quantidade: number;
  valorUnitarioEntrada: number;
  valorTotal: number;
  valorIcmsSt: number;
  valorIpi: number;
  valorFcpSt: number;
  custoAdicional: number;
  volumeUnitario?: boolean;
  bloquearAlteracao?: boolean;
};

export type ProdutoObterResponse = {
  custoAdicional: number;
  id: string;
  participaRateioIcmsSt: boolean;
  quantidadeEntrada: number;
  valorFcpSt: number;
  valorIcmsSt: number;
  valorIpi: number;
  valorUnitarioEntrada: number;
  variacoes: {
    cor: string | null;
    corId: string | null;
    nome: string;
    produtoCorTamanhoId: string;
    produtoId: string;
    quantidade: number;
    tamanho: string | null;
    tamanhoId: string | null;
  }[];
  volumeUnitario: boolean;
};

export type ProdutoPaginadoRetorno = {
  totalProdutos: number;
  totalItens: number;
  valorTotal: number;
  registros: Produto[];
  bloquearAlteracao: boolean;
};

export type AdicionarProdutoRetorno = {
  id: string;
  tamanhoId?: string;
};

export type ProdutoResponse = {
  produtoId: string;
  variacoes: {
    corId: string | null;
    tamanhoId: string | null;
    quantidade: number;
  }[];
  valorUnitarioEntrada: number;
  valorIpi: number;
  valorIcmsSt: number;
  valorFcpSt: number;
  custoAdicional: number;
};

export type ObterProdutosDaListaProps = {
  currentPage: number;
  pageSize: number;
  orderColumn: string;
  orderDirection: string;
  zerarLista?: boolean;
};
