import {
  ModalContent,
  ModalBody,
  useDisclosure,
  ModalHeader,
  Button,
  GridItem,
  Collapse,
  FormLabel,
  Flex,
  useMediaQuery,
  Text,
  Icon,
  Box,
} from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { create } from 'react-modal-promise';

import { ModalConsultaProdutosEntradaMercadoria } from 'pages/EntradaMercadoria/components/ConsultarProdutos';
import { MenuSelect } from 'pages/EntradaMercadoria/components/MenuSelect';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import CreatableSelect from 'components/PDV/Select/CreatableSelect';
import CreatableSelectVirtualized from 'components/PDV/Select/CreatableSelectVirtualized';
import { chakraComponents } from 'components/PDV/Select/ReactSelectIntegracao';
import { SimpleCard } from 'components/update/Form/SimpleCard';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import { IconPesquisaCodigoBarras, LupaIcon } from 'icons';

import {
  Quantidade,
  Footer,
  FormularioValoresFiscais,
  ListagemVariacoesAdicionadas,
  Tamanho,
} from './components';
import { useModalAdicionarProduto } from './hooks';
import {
  ModalAdicionarProdutoProps,
  ModalAdicionarProdutoResponse,
  FormData,
} from './validationForm';

export const ModalAdicionarProduto = create<
  ModalAdicionarProdutoProps,
  ModalAdicionarProdutoResponse
>(
  ({
    onResolve,
    onReject,
    casasDecimaisQuantidade,
    casasDecimaisValor,
    entradaRateiaIcmsSt,
    adicionarProduto,
    ...rest
  }) => {
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
    const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

    const fecharModal = () => {
      onReject();
      onClose();
    };

    const formMethods = useForm<FormData>({
      defaultValues: {
        cor: null,
      },
    });

    const {
      buscarProduto,
      quantidadeMaiorQueZero,
      handleConfirmarAdicionarNovo,
      handleConfirmarSair,
      handleCadastrarCor,
      handleCadastrarTamanho,
      handleCadastrarProduto,
      handleAbrirModalEscolherGradeTamanhos,
      onChangeSelectProduto,
      adicionarVariacaoNaLista,
      setListaVariacoes,
      validarPesquisaPorCodigo,
      pesquisarPorLeitorWatch,
      isLoading,
      isLoadingProduto,
      totalRegistros,
      temGradeLancada,
      listaVariacoes,
      produtoDeVolumeUnitario,
      produtoTipoVariacao,
      produtoTemCores,
      produtoTemTamanhos,
      corEscolhida,
      podeConfirmar,
      produtoSelecionado,
      abriModalVariacoes,
      coresDoProduto,
      tamanhosDoProduto,
      limparValoresFiscaisVoltarValorPrecoCompra,
      setValue,
    } = useModalAdicionarProduto(
      formMethods,
      casasDecimaisQuantidade,
      adicionarProduto,
      entradaRateiaIcmsSt,
      onResolve,
      fecharModal
    );

    return (
      <ModalPadraoChakra
        isCentered={isLargerThan900}
        size="full"
        {...rest}
        isOpen={isOpen}
        onClose={() => {
          fecharModal();
        }}
      >
        <ModalContent h="unset" bg="gray.100" borderRadius="0px">
          {isLoading && <LoadingPadrao />}
          <ModalHeader
            px={['16px', '40px']}
            py="20px"
            color="violet.500"
            fontWeight="normal"
            fontSize="18px"
          >
            Adicionar Produto
          </ModalHeader>
          <ModalBody px={['16px', '40px']} pt="0px">
            <FormProvider {...formMethods}>
              <SimpleCard boxShadow="none" bg="violet.500" p="16px">
                <Flex
                  justify="space-between"
                  width="full"
                  gap={6}
                  direction={{ base: 'column', md: 'row' }}
                  align={{ base: 'end', md: 'center' }}
                >
                  <Box width={{ base: 'full', lg: '60%' }}>
                    <CreatableSelectVirtualized
                      id="produto"
                      name="produto"
                      placeholder={
                        isLargerThan700
                          ? 'Digite o nome do produto ou utilize um leitor de códigos'
                          : 'Digite o nome do produto ou utilize um leitor'
                      }
                      handleGetOptions={buscarProduto}
                      isLoading={isLoadingProduto}
                      creatableButtonShow={!pesquisarPorLeitorWatch}
                      creatableInputTextPreffix="Cadastrar o produto"
                      handleCreateOption={(inputValue) => {
                        if (!validarPesquisaPorCodigo(inputValue)) {
                          return handleCadastrarProduto(inputValue);
                        }
                      }}
                      onChangeSelect={onChangeSelectProduto}
                      required
                      asControlledByObject
                      isClearable
                      autoFocus
                      closeMenuOnSelect
                      totalRegistros={totalRegistros}
                    />
                  </Box>
                  <Flex
                    gap="6px"
                    px={2}
                    cursor="pointer"
                    width="min"
                    align="center"
                  >
                    <Icon as={LupaIcon} fontSize="20px" color="white" />
                    <Text
                      color="white"
                      textDecor="underline"
                      fontSize="16px"
                      width="max"
                      onClick={async () => {
                        const response =
                          await ModalConsultaProdutosEntradaMercadoria({
                            casasDecimais: {
                              casasDecimaisQuantidade,
                            },
                          });

                        const produtoOpcao = {
                          label: response.nome,
                          value: {
                            ...response,
                            coresOptions: [],
                            tamanhosOptions: [],
                          } as any,
                        };

                        setValue('produto', produtoOpcao);
                        onChangeSelectProduto(produtoOpcao);
                      }}
                    >
                      Consultar produtos
                    </Text>
                  </Flex>
                </Flex>
                <SimpleGridForm gap="8px">
                  {isLargerThan700 && pesquisarPorLeitorWatch && (
                    <GridItem colSpan={1}>
                      <Flex
                        bg="transparent"
                        cursor="pointer"
                        borderStyle="solid"
                        transition="all 300ms"
                        align="center"
                        height="full"
                        justifyContent="flex-start"
                        alignItems="center"
                        borderRadius="5px"
                        pl="8px"
                        pt="8px"
                        w="fit-content"
                      >
                        <IconPesquisaCodigoBarras
                          width="22px"
                          height="22px"
                          stroke="#A8E303"
                        />
                      </Flex>
                    </GridItem>
                  )}
                </SimpleGridForm>
              </SimpleCard>
              <Collapse
                in={!!produtoSelecionado && !isLoadingProduto}
                animateOpacity
              >
                {produtoTipoVariacao && (
                  <FormLabel
                    fontSize="14px"
                    fontWeight="600"
                    color="black"
                    mt="19px"
                    mb="0px"
                  >
                    Informe uma variação
                  </FormLabel>
                )}
                <SimpleCard
                  background={produtoTipoVariacao ? 'gray.200' : 'gray.100'}
                  boxShadow="none"
                  p="28px 16px"
                  px={produtoTipoVariacao ? '40px' : '16px'}
                  pb={produtoTipoVariacao ? '48px' : '28px'}
                >
                  <SimpleGridForm>
                    {produtoTemCores && (
                      <CreatableSelect
                        id="cor"
                        name="cor"
                        label="Cor"
                        placeholder="Selecione uma cor cadastrada no produto"
                        creatableInputTextPreffix="Cadastrar a cor"
                        colSpan={[12, 12, 4, 4]}
                        handleCreateOption={handleCadastrarCor}
                        options={coresDoProduto}
                        disabled={isLoadingProduto}
                        asControlledByObject
                        required
                        menuPortalTarget={document?.body}
                        actionLinkText="Adicionar nova"
                        components={{
                          ...chakraComponents,
                          MenuList: (props: any) => (
                            <MenuSelect
                              texto={
                                coresDoProduto?.length > 0
                                  ? '**Exibindo apenas cores ativas do produto**'
                                  : ''
                              }
                              {...props}
                            />
                          ),
                        }}
                        actionLinkOnClick={() => abriModalVariacoes()}
                      />
                    )}
                    {produtoTemTamanhos && (
                      <Tamanho
                        temGradeLancada={temGradeLancada}
                        tamanhosDoProduto={tamanhosDoProduto}
                        handleCadastrarTamanho={handleCadastrarTamanho}
                        isLoadingProduto={isLoadingProduto}
                        produtoTemCores={produtoTemCores}
                        corEscolhida={corEscolhida}
                        handleAbrirModalEscolherGradeTamanhos={
                          handleAbrirModalEscolherGradeTamanhos
                        }
                        produtoTemTamanhos={produtoTemTamanhos}
                        abrirModalVariacoes={abriModalVariacoes}
                      />
                    )}

                    <Quantidade
                      produtoTemGradeLancada={temGradeLancada}
                      casasDecimaisQuantidade={casasDecimaisQuantidade}
                      produtoDeVolumeUnitario={!!produtoDeVolumeUnitario}
                      produtoPossuiVariacoes={produtoTipoVariacao}
                      quantidade={formMethods
                        ?.watch('listaTamanhoIdQuantidade')
                        ?.reduce((acc, curr) => acc + curr.quantidade, 0)}
                    />
                  </SimpleGridForm>
                  <SimpleGridForm mt="20px" columns={[12, 12, 12, 12]}>
                    <FormularioValoresFiscais
                      entradaRateiaIcmsSt={entradaRateiaIcmsSt}
                      casasDecimaisValor={casasDecimaisValor}
                      listaJaPossuiProdutoAdicionado={!!listaVariacoes?.length}
                    />
                    {produtoTipoVariacao && (
                      <GridItem colSpan={[12, 12, 2]} mt="18px" ml="0px">
                        <Button
                          colorScheme="teal"
                          borderRadius="full"
                          w="full"
                          height="36px"
                          fontSize="14px"
                          minW="176px"
                          onClick={adicionarVariacaoNaLista}
                          isDisabled={
                            !podeConfirmar || !quantidadeMaiorQueZero()
                          }
                          fontWeight="600"
                        >
                          Adicionar variação
                        </Button>
                      </GridItem>
                    )}
                    <ListagemVariacoesAdicionadas
                      listaVariacoes={listaVariacoes}
                      setListaVariacoes={setListaVariacoes}
                      casasDecimaisQuantidade={casasDecimaisQuantidade}
                      produtoTemCores={produtoTemCores}
                      produtoTemTamanhos={produtoTemTamanhos}
                      limparValoresFiscaisVoltarValorPrecoCompra={
                        limparValoresFiscaisVoltarValorPrecoCompra
                      }
                    />
                  </SimpleGridForm>
                </SimpleCard>
              </Collapse>
            </FormProvider>
          </ModalBody>
          <Footer
            onReject={onReject}
            onClose={onClose}
            handleConfirmarSair={handleConfirmarSair}
            handleConfirmarAdicionarNovo={handleConfirmarAdicionarNovo}
            isDisabledConfirmar={
              listaVariacoes?.length > 0
                ? false
                : !podeConfirmar || !quantidadeMaiorQueZero()
            }
          />
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
