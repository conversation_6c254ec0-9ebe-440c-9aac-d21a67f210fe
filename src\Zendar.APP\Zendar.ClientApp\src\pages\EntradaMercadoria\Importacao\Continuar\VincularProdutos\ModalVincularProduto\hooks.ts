import { useCallback, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';
import {
  checarPesquisaPorCodigoBarras,
  checarPesquisaPorSKU,
  checarPesquisaPorGtinEan,
} from 'helpers/validation/checarCodigo';

import api, { ResponseApi } from 'services/api';

import {
  ProdutoCoresProps,
  ProdutoOptionProps,
  TipoCadastro,
  NewOption,
} from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';
import { ModalCadastrarCor } from 'pages/EntradaMercadoria/ModalCadastrarCor';
import { ModalCadastrarProduto } from 'pages/EntradaMercadoria/ModalCadastrarProduto';
import { ModalCadastrarTamanho } from 'pages/EntradaMercadoria/ModalCadastrarTamanho';

import { GridPaginadaRetorno } from 'components/Grid/Paginacao';
import { ModalGradeTamanhos } from 'components/update/Modal/ModalGradeTamanhos';
import { PaginationData } from 'components/update/Pagination';

import OptionType from 'types/optionType';

import { obterProdutoCorTamanhoGtinEan } from 'api/Produtos/CorTamanho/ObterProdutoCorTamanhoGtiEan';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum from 'constants/enum/statusConsulta';
import TipoProdutoEnum from 'constants/enum/tipoProduto';
import ConstanteFuncionalidades from 'constants/permissoes';

import { ModalCriarVariacao } from './components/ModalCriarVariacao';
import {
  ItemVinculadoObter,
  ModalVincularProdutosResponse,
  ProdutoTamanhoProps,
} from './types';
import {
  TamanhoQuantidade,
  FormData,
  ImportacaoPropsEditar,
  ItemEdicaoProps,
} from './validationForm';

export const useVinculacao = ({
  produto,
  onReject,
  formMethods,
  entradaMercadoriaId,
  onResolve,
  casasDecimaisQuantidade,
  temProximoProdutoParaVincular,
}: {
  produto: {
    documentoFiscalItemId: string;
    codigoGTINEAN: string;
    codigoBarrasNota?: string | null;
    valorTotal: number;
    quantidade: number;
    valorUnitario: number;
    cfop: string;
    codigoCest: string;
    ncm: string;
  };
  onReject: () => void;
  formMethods: UseFormReturn<FormData>;
  entradaMercadoriaId: string;
  onResolve: (response: ModalVincularProdutosResponse) => void;
  casasDecimaisQuantidade: number;
  temProximoProdutoParaVincular: boolean;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingProduto, setIsLoadingProduto] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [listaVariacoes, setListaVariacoes] = useState<any[]>([]);
  const [itemEdicao, setItemEdicao] = useState<ItemEdicaoProps['itens'] | null>(
    null
  );
  const [informacoesVinculacaoEdicao, setInformacoesVinculacaoEdicao] =
    useState<ImportacaoPropsEditar | null>(null);

  const { reset, watch, setValue, setFocus } = formMethods;
  const {
    pesquisarPorLeitor: pesquisarPorLeitorWatch,
    listaTamanhoIdQuantidade,
    cor: corWatch,
    produto: produtoWatch,
    quantidade: quantidadeWatch,
    codigoBarras,
    codigoBarrasNota,
  } = watch();

  const { permitido: temPermissaoCadastrarProduto } = auth.possuiPermissao(
    ConstanteFuncionalidades.PRODUTO_CADASTRAR
  );
  const { permitido: temPermissaoCadastrarCor } = auth.possuiPermissao(
    ConstanteFuncionalidades.COR_CADASTRAR
  );
  const { permitido: temPermissaoCadastrarTamanho } = auth.possuiPermissao(
    ConstanteFuncionalidades.TAMANHO_CADASTRAR
  );

  const produtoSelecionado = produtoWatch?.value;
  const corEscolhida = corWatch;

  const coresDoProduto = produtoWatch?.value?.coresOptions || [];
  const tamanhosDoProduto =
    corEscolhida?.tamanhos || produtoSelecionado?.tamanhosOptions || [];

  const produtoTipoSimples =
    produtoWatch?.value?.tipoProduto === TipoProdutoEnum.PRODUTO_SIMPLES;
  const produtoTipoVariacao =
    produtoWatch?.value?.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;
  const produtoDeVolumeUnitario = produtoWatch?.value?.volumeUnitario;
  const produtoTemCores = coresDoProduto?.length > 0;
  const produtoTemTamanhos = tamanhosDoProduto?.length > 0;

  const tamanhoEscolhido =
    (listaTamanhoIdQuantidade || [])?.length > 0 &&
    listaTamanhoIdQuantidade &&
    listaTamanhoIdQuantidade[0]?.tamanho;

  const quantidadeTotal = listaVariacoes.reduce(
    (acc, curr) =>
      acc +
      curr.listaTamanhoIdQuantidade.reduce(
        (
          acc2: number,
          curr2: {
            quantidade: number;
          }
        ) => acc2 + curr2.quantidade,
        0
      ),
    0
  );

  const quantidadeProdutoEscolhido =
    (listaTamanhoIdQuantidade || [])[0]?.quantidade || 0;

  const temGradeLancada = (listaTamanhoIdQuantidade || []).length > 1;

  const abriModalVariacoes = useCallback(async () => {
    const produtoId = produtoWatch?.value?.id;

    const dadosVariacoes = await ModalCriarVariacao({
      produtoId,
    });

    if (dadosVariacoes?.sucesso) {
      setValue('produto.value.coresOptions', dadosVariacoes?.listaCores);
      setValue('produto.value.tamanhosOptions', dadosVariacoes?.listaTamanhos);
    }
  }, [produtoWatch, setValue]);

  function transformarDadosApi(
    entrada: ItemEdicaoProps
  ): ImportacaoPropsEditar {
    const dados = {
      entradaMercadoriaId: entradaMercadoriaId,
      documentoFiscalItemId: produto.documentoFiscalItemId,
      quantidadeEntrada: entrada.quantidadeEntrada,
      valorUnitarioEntrada: entrada.valorUnitarioEntrada,
      cfopEntrada: entrada.cfopEntrada,
      listaItensParaVinculacao: entrada.itens.map((item) => ({
        codigoGTINEAN: produto.codigoGTINEAN,
        custoAdicional: item.custoAdicional,
        corId: item.cor.id,
        listaTamanhoIdQuantidade: [
          {
            quantidade: item.quantidade,
            id: item.tamanho.id,
            descricaoTamanho: item.tamanho.descricao,
          },
        ],
        produtoId: item.produto.id,
        valorUnitario: item.valorUnitario,
      })),
    };
    setItemEdicao(entrada?.itens);
    return dados;
  }

  const alterarVinculacao = formMethods.handleSubmit(
    useCallback(
      async (data) => {
        setIsLoading(true);

        const novoCodigoBarras = data?.codigoBarras
          ? String(data.codigoBarras)
          : null;

        const existeNovoProdutoVinculado = !!data.produto?.value;
        const novoProdutoVinculado = existeNovoProdutoVinculado
          ? {
              nome: data.produto?.value?.nome ?? '',
              tipoProduto:
                data.produto?.value?.tipoProduto ??
                TipoProdutoEnum.PRODUTO_SIMPLES,
            }
          : null;

        const dados = {
          ...informacoesVinculacaoEdicao,
          cfopEntrada: data.cfop,
          codigoGTINEAN: novoCodigoBarras,
        };

        const response = await api.post<void, ResponseApi<string>>(
          ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_VINCULAR_ITEM,
          dados
        );

        if (response) {
          if (response.avisos) {
            response.avisos.map((aviso) => toast.warning(aviso));
          }

          onResolve({
            success: response.sucesso,
            quantidade: data.quantidade,
            valorUnitario: data.valorUnitario,
            cfop: data.cfop,
            codigoBarrasCadastro: novoCodigoBarras,
            temProximoProdutoParaVincular: false,
            produtoVinculado: novoProdutoVinculado,
          });
        }

        setIsLoading(false);
      },
      [informacoesVinculacaoEdicao, produtoSelecionado]
    )
  );

  async function obterItemVinculado() {
    if (produto.documentoFiscalItemId) {
      setIsLoading(true);

      const response = await api.get<void, ResponseApi<ItemVinculadoObter>>(
        ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_OBTER_ITENS_VINCULADOS,
        {
          params: {
            documentoFiscalItemId: produto.documentoFiscalItemId,
          },
        }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso: string) => toast.warning(aviso));
        }

        if (response.sucesso && response.dados) {
          const dadosVinculacao = transformarDadosApi(response.dados);

          const newFormValues: FormData = {
            quantidade: response.dados.quantidadeEntrada,
            valorUnitario: response.dados.valorUnitarioEntrada,
            cfop: response.dados.cfopEntrada,
            cfopNota: response.dados.cfopNota,
            codigoBarras,
            codigoBarrasNota,
          };

          reset(newFormValues);
          setInformacoesVinculacaoEdicao(dadosVinculacao);
          setIsLoading(false);
          return;
        }
      }

      onReject();
    }
  }

  const handleSubmit = async ({
    data,
    vincularProximoItem,
  }: {
    data: FormData;
    vincularProximoItem?: boolean;
  }) => {
    setIsLoading(true);

    const produtoDoTipoSimples =
      produtoWatch?.value?.tipoProduto === TipoProdutoEnum.PRODUTO_SIMPLES;

    const novoCodigoBarrasProdutoSimples =
      produtoDoTipoSimples && data.codigoBarras
        ? String(data.codigoBarras)
        : null;

    const existeNovoProdutoVinculado = !!data.produto?.value;
    const novoProdutoVinculado = existeNovoProdutoVinculado
      ? {
          nome: data.produto.value?.nome ?? '',
          tipoProduto:
            data.produto.value?.tipoProduto ?? TipoProdutoEnum.PRODUTO_SIMPLES,
        }
      : null;

    const listaItensParaVinculacao = listaVariacoes.map((item) => {
      const produtoGTINEAN = produto?.codigoGTINEAN;
      const possuiApenasUmaVariacao =
        item.listaTamanhoIdQuantidade.length === 1 &&
        listaVariacoes.length === 1;
      const codigoGTINEAN =
        item.codigoBarras ||
        (produtoGTINEAN && possuiApenasUmaVariacao ? produtoGTINEAN : null);

      return {
        produtoId: item.produtoId,
        corId: item.corId || null,
        quantidade: item.quantidade,
        listaTamanhoIdQuantidade: item.listaTamanhoIdQuantidade.map(
          (tamanho: {
            id: string;
            quantidade: number;
            codigoBarras: string | null;
          }) => ({
            quantidade: tamanho.quantidade,
            id: tamanho.id || null,
            codigoGTINEAN:
              tamanho.id && tamanho.codigoBarras ? tamanho.codigoBarras : null,
          })
        ),
        valorUnitario: data.valorUnitario,
        custoAdicional: item.custoAdicional,
        codigoGTINEAN,
      };
    });

    const itemParaVinculacao = [
      {
        codigoGTINEAN: data.codigoBarras ? String(data.codigoBarras) : null,
        custoAdicional: data.custoAdicional,
        listaTamanhoIdQuantidade: data.listaTamanhoIdQuantidade,
        produtoId: produtoSelecionado?.id,
        valorUnitario: data.valorUnitario,
      },
    ];

    const dataApi = {
      entradaMercadoriaId,
      documentoFiscalItemId: produto.documentoFiscalItemId,
      quantidadeEntrada: data.quantidade,
      valorUnitarioEntrada: data.valorUnitario,
      cfopEntrada: data.cfop,
      codigoGTINEAN: novoCodigoBarrasProdutoSimples,
      listaItensParaVinculacao: produtoDoTipoSimples
        ? itemParaVinculacao
        : listaItensParaVinculacao,
    };

    const response = await api.post<void, ResponseApi<string>>(
      ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_VINCULAR_ITEM,
      dataApi
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.map((aviso) => toast.warning(aviso));
      }

      onResolve({
        success: response.sucesso,
        quantidade: data.quantidade,
        valorUnitario: data.valorUnitario,
        cfop: data.cfop,
        codigoBarrasCadastro: novoCodigoBarrasProdutoSimples,
        temProximoProdutoParaVincular:
          vincularProximoItem ?? temProximoProdutoParaVincular,
        produtoVinculado: novoProdutoVinculado,
      });
    }

    setIsLoading(false);
  };

  const podeConfirmar = useCallback(() => {
    if (produtoTipoSimples) {
      return true;
    }

    if (produtoTemCores && produtoTemTamanhos) {
      return !!corEscolhida && !!tamanhoEscolhido;
    }

    if (produtoTemCores) {
      return !!corEscolhida;
    }

    if (produtoTemTamanhos) {
      return !!tamanhoEscolhido;
    }

    return false;
  }, [
    corEscolhida,
    produtoTemCores,
    produtoTemTamanhos,
    produtoTipoSimples,
    tamanhoEscolhido,
  ])();

  const handleFocus = useCallback(
    async (
      input:
        | 'custoAdicional'
        | 'listaTamanhoIdQuantidade.0.quantidade'
        | 'cor'
        | 'listaTamanhoIdQuantidade.0.tamanho'
        | 'produto'
    ) => {
      setIsLoading(true);

      await new Promise((resolve) => {
        setTimeout(() => {
          try {
            if (input) formMethods?.setFocus(input);
            resolve(null);
          } catch (error) {
            setIsLoading(false);
          }
        }, 1000);
      });

      setIsLoading(false);
    },
    [formMethods]
  );

  const limparCampoProduto = useCallback(() => {
    setValue('produto', null);
  }, [setValue]);

  const resetarQuantidadeTamanho = useCallback(() => {
    setValue('listaTamanhoIdQuantidade.0.quantidade', 1);
    setValue('listaTamanhoIdQuantidade.0.tamanho', null);
    setValue('listaTamanhoIdQuantidade', []);
  }, [setValue]);

  const limparCamposValoresFiscais = useCallback(() => {
    setValue('custoAdicional', 0);
  }, [setValue]);

  const limparValoresFiscaisVoltarValorPrecoCompra = useCallback(() => {
    setValue('custoAdicional', 0);
  }, []);

  const quantidadeMaiorQueZero = () => {
    const listaProdutos = watch('listaTamanhoIdQuantidade') || [];
    const isQuantidadeValida = listaProdutos?.every(
      (element: { quantidade: number }) => element.quantidade > 0
    );
    if (isQuantidadeValida) return true;

    return false;
  };

  const handleAbrirModalEscolherGradeTamanhos = async () => {
    if (produtoTemTamanhos) {
      const tamanhos = tamanhosDoProduto?.map(
        (tamanho: { value: string; label: string }) => {
          const tamanhoQuantidade = (listaTamanhoIdQuantidade || []).find(
            (tamanhoIdQuantidade: {
              tamanho: {
                value: string;
              };
              quantidade: number;
            }) => tamanhoIdQuantidade.tamanho?.value === tamanho.value
          );

          return {
            produtoCorTamanhoId: tamanho.value,
            tamanho: tamanho.label,
            padraoSistema: false,
            quantidade: tamanhoQuantidade?.quantidade || 0,
          };
        }
      );
      try {
        const tamanhosInformadosNoModalGrade = await ModalGradeTamanhos({
          produtoNome: produtoWatch?.label || '',
          corDescricao: corEscolhida?.label,
          casasDecimaisQuantidade,
          volumeUnitario: produtoDeVolumeUnitario,
          tamanhos,
          limiteQuantidade: quantidadeWatch - quantidadeTotal,
        });

        const novaListaTamanhosIdQuantidade =
          tamanhosInformadosNoModalGrade?.length > 0
            ? tamanhosInformadosNoModalGrade?.map(
                (tamanho) =>
                  ({
                    quantidade: tamanho.quantidade,
                    tamanho: {
                      value: tamanho.produtoCorTamanhoId,
                      label: tamanho.tamanhoDescricao,
                    },
                  } as TamanhoQuantidade)
              )
            : [];

        if (tamanhosInformadosNoModalGrade?.length === 1) {
          const unicoTamanhoInformado = tamanhosInformadosNoModalGrade[0];

          setValue('listaTamanhoIdQuantidade.0.tamanho', {
            value: unicoTamanhoInformado?.produtoCorTamanhoId,
            label: unicoTamanhoInformado?.tamanhoDescricao || '',
          });
          setValue(
            'listaTamanhoIdQuantidade.0.quantidade',
            unicoTamanhoInformado.quantidade
          );
        }

        setValue('listaTamanhoIdQuantidade', novaListaTamanhosIdQuantidade);
        return true;
      } catch (error) {
        return false;
      }
    }
    return false;
  };

  const adicionarVariacaoNaLista = useCallback(() => {
    const data: FormData = watch();

    const obterCodigoPorVariacao = (tamanhoId?: string) => {
      const codigoBarrasInformadoNoCampo = data?.codigoBarras
        ? String(data.codigoBarras)
        : null;

      if (!tamanhoId || temGradeLancada) return codigoBarrasInformadoNoCampo;

      return (
        data.cor?.tamanhos?.find((tamanho: any) => tamanho.value === tamanhoId)
          ?.codigoBarras || null
      );
    };

    const produto = {
      id: data.produto?.value.id || '',
      produtoId: data.produto?.value.id || '',
      corId: data.cor?.value || '',
      corDescricao: data.cor?.label || '',
      listaTamanhoIdQuantidade:
        data.listaTamanhoIdQuantidade?.map(
          (item: {
            tamanho: OptionType<{ id: string; label: string }>;
            quantidade: number;
          }) => ({
            id: item.tamanho?.value || '',
            tamanhoDescricao: item.tamanho?.label || '',
            quantidade: item.quantidade,
            codigoBarras: obterCodigoPorVariacao(
              item?.tamanho?.value as unknown as string
            ),
          })
        ) || [],
      valorUnitarioEntrada: data.valorUnitario,
      custoAdicional: data.custoAdicional,
      codigoBarras: data.codigoBarras ? String(data.codigoBarras) : null,
    };

    if (produtoTemCores && !produtoTemTamanhos) {
      setListaVariacoes((prev) => {
        const variacaoCorJaAdicionada = prev.find(
          (item) => item.corId === produto.corId
        );

        if (variacaoCorJaAdicionada) {
          return prev.map((item) =>
            item.corId === produto.corId
              ? {
                  ...item,
                  listaTamanhoIdQuantidade: [
                    {
                      id: item.listaTamanhoIdQuantidade[0].id,
                      quantidade:
                        item.listaTamanhoIdQuantidade[0].quantidade +
                        produto.listaTamanhoIdQuantidade[0].quantidade,
                      tamanhoDescricao:
                        item.listaTamanhoIdQuantidade[0].tamanhoDescricao,
                    },
                  ],
                }
              : item
          );
        }

        return [...prev, produto];
      });

      resetarQuantidadeTamanho();
      limparCamposValoresFiscais();
      setValue('codigoBarrasVariacao', null);
      setValue('cor', null);
      setFocus('cor');
      return;
    }

    if (!produtoTemCores && produtoTemTamanhos) {
      setListaVariacoes((prev) => {
        const variacaoTamanhoJaAdicionada = prev.find((item) =>
          item.listaTamanhoIdQuantidade.find(
            (tamanho: { id: string; quantidade: number }) =>
              tamanho.id === produto.listaTamanhoIdQuantidade[0].id
          )
        );

        if (variacaoTamanhoJaAdicionada) {
          return prev.map((item) =>
            item.listaTamanhoIdQuantidade.find(
              (tamanho: { id: string; quantidade: number }) =>
                tamanho.id === produto.listaTamanhoIdQuantidade[0].id
            )
              ? {
                  ...item,
                  listaTamanhoIdQuantidade: [
                    {
                      id: item.listaTamanhoIdQuantidade[0].id,
                      quantidade:
                        item.listaTamanhoIdQuantidade[0].quantidade +
                        produto.listaTamanhoIdQuantidade[0].quantidade,
                      tamanhoDescricao:
                        item.listaTamanhoIdQuantidade[0].tamanhoDescricao,
                    },
                  ],
                }
              : item
          );
        }

        return [...prev, produto];
      });

      resetarQuantidadeTamanho();
      limparCamposValoresFiscais();
      setValue('codigoBarrasVariacao', null);
      setValue('listaTamanhoIdQuantidade.0.tamanho', null);
      setFocus('listaTamanhoIdQuantidade.0.tamanho');
      return;
    }

    setListaVariacoes((prev) => {
      const variacaoCorJaAdicionada = prev.find(
        (item) => item.corId === produto.corId
      );

      const variacaoTamanhoJaAdicionada = prev.find((item) => {
        if (item.corId === produto.corId) {
          return item.listaTamanhoIdQuantidade.find(
            (tamanho: { id: string; quantidade: number }) =>
              tamanho.id === produto.listaTamanhoIdQuantidade[0].id
          );
        }
        return false;
      });

      if (variacaoCorJaAdicionada && variacaoTamanhoJaAdicionada) {
        return prev.map((item) => {
          if (item.corId === produto.corId) {
            return {
              ...item,
              listaTamanhoIdQuantidade: item.listaTamanhoIdQuantidade.map(
                (tamanho: { id: string; quantidade: number }) => ({
                  ...tamanho,
                  quantidade:
                    tamanho.id === produto.listaTamanhoIdQuantidade[0].id
                      ? tamanho.quantidade +
                        produto.listaTamanhoIdQuantidade[0].quantidade
                      : tamanho.quantidade,
                })
              ),
            };
          }

          return item;
        });
      }

      if (variacaoCorJaAdicionada && !variacaoTamanhoJaAdicionada) {
        return prev.map((item) => {
          if (item.corId === produto.corId) {
            return {
              ...item,
              listaTamanhoIdQuantidade: [
                ...item.listaTamanhoIdQuantidade,
                ...produto.listaTamanhoIdQuantidade,
              ],
            };
          }
          return item;
        });
      }

      return [...prev, produto];
    });
    resetarQuantidadeTamanho();
    limparCamposValoresFiscais();
    setValue('cor', null);
    setValue('codigoBarrasVariacao', null);
    setFocus('cor');
  }, [
    watch,
    produtoTemCores,
    produtoTemTamanhos,
    resetarQuantidadeTamanho,
    limparCamposValoresFiscais,
    setValue,
    setFocus,
  ]);

  const buscarTamanhoDoProduto = useCallback(async (id: string) => {
    setIsLoadingProduto(true);

    const response = await api.get<void, ResponseApi<ProdutoTamanhoProps[]>>(
      `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${id}/tamanhos`,
      { params: { status: StatusConsultaEnum.ATIVOS } }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        const newSizes = response.dados
          .filter(({ padraoSistema }) => !padraoSistema)
          .map((size, index) => ({
            label: size.descricao,
            value: size.id,
            produtoCorTamanhoId: response.dados[index].id,
          }));

        setIsLoadingProduto(false);
        return newSizes;
      }
    }

    setIsLoadingProduto(false);
    return [];
  }, []);

  const buscarCoresDoProduto = useCallback(async (id: string) => {
    setIsLoadingProduto(true);

    const response = await api.get<void, ResponseApi<ProdutoCoresProps[]>>(
      `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${id}/cores`,
      { params: { status: StatusConsultaEnum.ATIVOS } }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        const newColors = response.dados
          .filter(({ cor }) => !cor.padraoSistema)
          .map(({ cor }, index) => ({
            label: cor.descricao,
            value: cor.id,
            produtoCorId: response.dados[index].id,
          }));

        setIsLoadingProduto(false);
        return newColors;
      }
    }

    setIsLoadingProduto(false);
    return [];
  }, []);

  const obterCoresETamanhos = async (produtoId: string) => {
    const response = await obterProdutoCorTamanhoGtinEan({
      produtoId,
    });

    if (response?.avisos) {
      response.avisos.forEach((aviso) => toast.warning(aviso));
    }

    if (!response?.sucesso || !response?.dados) {
      return null;
    }

    const produtoCorTamanho = response.dados;

    const cores = produtoCorTamanho
      .filter(({ cor }) => !cor.padraoSistema)
      .map(({ cor, tamanhos }) => {
        const possuiApenasTamanhoPadrao = tamanhos?.every(
          ({ padraoSistema }) => padraoSistema
        );

        const opcoesTamanhos = tamanhos
          ?.filter(({ padraoSistema }) => !padraoSistema)
          .map((tamanho) => ({
            label: tamanho.descricao,
            value: tamanho.id,
            produtoCorId: tamanho.id,
            codigoBarras: tamanho.codigoGTINEAN,
          }));

        return {
          label: cor.descricao,
          value: cor.id,
          produtoCorId: cor.id,
          tamanhos: opcoesTamanhos,
          codigoBarras: possuiApenasTamanhoPadrao
            ? (tamanhos || [])[0].codigoGTINEAN
            : null,
        };
      });

    const tamanhosInicial = produtoCorTamanho[0]?.tamanhos
      ?.filter(({ padraoSistema }) => !padraoSistema)
      .map((tamanho) => ({
        label: tamanho.descricao,
        value: tamanho.id,
        produtoCorId: tamanho.id,
        codigoBarras: tamanho.codigoGTINEAN,
      }));

    return { cores, tamanhos: tamanhosInicial };
  };

  const adicionarProdutoAutomaticamente = useCallback(
    async (option: OptionType<ProdutoOptionProps>) => {
      const { value } = option;

      const possuiVariacao =
        value.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;

      if (!possuiVariacao) {
        setValue('produto', {
          ...option,
        });
        setValue('cor', null);
        setValue('listaTamanhoIdQuantidade.0.tamanho', null);
        setValue('listaTamanhoIdQuantidade.0.quantidade', quantidadeWatch);

        if (value?.codigoGTINEAN) {
          setValue('codigoBarras', value.codigoGTINEAN || null);
        } else {
          setValue('codigoBarras', codigoBarrasNota || null);
        }

        return;
      }

      const response = await obterCoresETamanhos(value.id);
      const optionBuscadoPorCodigo = option as unknown as {
        value: {
          cor: string;
          corId: string;
          nome: string;
          produtoCorId: string;
          produtoCorTamanhoId: string;
          produtoId: string;
          tamanho: string;
          tamanhoId: string;
          tipoProduto: number;
          volumeUnitario: boolean;
          precoCompra: number;
          referencia: string;
        };
      };

      const tamanhoIdBuscado = optionBuscadoPorCodigo.value.tamanhoId;
      const corIdBuscado = optionBuscadoPorCodigo.value.corId;

      const encontrarTamanho = ({ value }: { value: string }) =>
        value === tamanhoIdBuscado;

      const cor = response?.cores.find(({ value }) => value === corIdBuscado);
      const tamanho =
        cor?.tamanhos?.find(encontrarTamanho) ||
        response?.tamanhos?.find(encontrarTamanho);

      const produtoCompleto = {
        label: optionBuscadoPorCodigo.value.nome,
        value: {
          id: optionBuscadoPorCodigo.value.produtoId,
          nome: optionBuscadoPorCodigo.value.nome,
          referencia: optionBuscadoPorCodigo.value.referencia,
          tipoProduto: optionBuscadoPorCodigo.value.tipoProduto,
          volumeUnitario: optionBuscadoPorCodigo.value.volumeUnitario,
          precoCompra: optionBuscadoPorCodigo.value.precoCompra,
          coresOptions: response?.cores || [],
          tamanhosOptions: response?.tamanhos || [],
        },
      };

      setValue('produto', produtoCompleto);
      setValue('cor', cor);
      setValue('codigoBarras', null);
      setValue('listaTamanhoIdQuantidade.0.tamanho', tamanho);
      setValue('listaTamanhoIdQuantidade.0.quantidade', 1);
    },
    [buscarCoresDoProduto, buscarTamanhoDoProduto, handleFocus, setValue]
  );

  const adicionarProdutoManualmente = useCallback(
    async (option: OptionType<ProdutoOptionProps>) => {
      const { value } = option;

      const possuiVariacao =
        value.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;

      if (!possuiVariacao) {
        setValue('produto', option);
        await handleFocus('custoAdicional');
        setValue('listaTamanhoIdQuantidade.0.quantidade', quantidadeWatch);

        if (value?.codigoGTINEAN) {
          setValue('codigoBarras', value.codigoGTINEAN || null);
        } else {
          setValue('codigoBarras', codigoBarrasNota || null);
        }

        return;
      }

      const response = await obterCoresETamanhos(value.id);

      const dadosProduto = {
        ...option,
        value: {
          ...option.value,
          coresOptions: response?.cores || [],
          tamanhosOptions: response?.tamanhos || [],
        },
      };

      setValue('produto', dadosProduto);
      setValue('codigoBarras', null);
      setValue('listaTamanhoIdQuantidade.0.quantidade', 1);
      await handleFocus(
        (response?.cores?.length || 0) > 0
          ? 'cor'
          : 'listaTamanhoIdQuantidade.0.tamanho'
      );
    },
    [buscarCoresDoProduto, buscarTamanhoDoProduto, handleFocus, setValue]
  );

  const onChangeSelectProduto = useCallback(
    async (
      option: OptionType<ProdutoOptionProps> | undefined | null,
      pesquisarPorLeitor?: boolean
    ) => {
      setIsLoading(true);
      limparCamposValoresFiscais();
      setListaVariacoes([]);
      resetarQuantidadeTamanho();
      if (!option) {
        setIsLoading(false);
        return;
      }

      if (pesquisarPorLeitor) {
        await adicionarProdutoAutomaticamente(option);
      } else {
        await adicionarProdutoManualmente(option);
      }
      setIsLoading(false);
    },
    [
      limparCamposValoresFiscais,
      resetarQuantidadeTamanho,
      adicionarProdutoAutomaticamente,
      adicionarProdutoManualmente,
    ]
  );

  const buscarProdutoPorCodigo = useCallback(
    async (codigo: string) => {
      setIsLoadingProduto(true);

      const response = await api.get<
        void,
        ResponseApi<
          {
            cor: string;
            corId: string;
            nome: string;
            produtoCorId: string;
            produtoCorTamanhoId: string;
            produtoId: string;
            tamanho: string;
            tamanhoId: string;
            tipoProduto: number;
            volumeUnitario: boolean;
            precoCompra: number;
            referencia: string;
          }[]
        >
      >(ConstanteEnderecoWebservice.LISTAR_SELECT_PRODUTO_CODIGO, {
        params: { codigoProduto: codigo },
      });

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        setTotalRegistros(response.dados?.length || 0);

        if (response?.sucesso && response?.dados) {
          const data = response.dados?.map((produtoBuscadoPorCodigo) => ({
            label: produtoBuscadoPorCodigo.nome,
            value: {
              ...produtoBuscadoPorCodigo,
              referencia: produtoBuscadoPorCodigo.referencia || '',
              tipoProduto: produtoBuscadoPorCodigo.tipoProduto,
              volumeUnitario: produtoBuscadoPorCodigo.volumeUnitario,
              precoCompra: produtoBuscadoPorCodigo.precoCompra,
              id: produtoBuscadoPorCodigo.produtoId,
              coresOptions: produtoBuscadoPorCodigo.cor
                ? [
                    {
                      label: produtoBuscadoPorCodigo.cor,
                      value: produtoBuscadoPorCodigo.produtoCorId,
                    },
                  ]
                : [],
              tamanhosOptions: produtoBuscadoPorCodigo.tamanho
                ? [
                    {
                      label: produtoBuscadoPorCodigo.tamanho,
                      value: produtoBuscadoPorCodigo.produtoCorTamanhoId,
                    },
                  ]
                : [],
            },
          }));
          if (data.length === 1) {
            const produtoBuscado = data[0];
            await onChangeSelectProduto(produtoBuscado, true);
            setIsLoadingProduto(false);
            const { value } = produtoBuscado;
            const possuiVariacao =
              value.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;
            if (possuiVariacao) {
              await handleFocus('custoAdicional');
            } else {
              await handleFocus('listaTamanhoIdQuantidade.0.quantidade');
            }
            return [];
          }

          setIsLoadingProduto(false);
          return data;
        }
      }

      setIsLoadingProduto(false);
      return [];
    },
    [handleFocus, onChangeSelectProduto]
  );

  const buscarProdutoDigitando = useCallback(
    async (
      valorDigitado: string,
      dataPagination: PaginationData
    ): Promise<OptionType<ProdutoOptionProps>[]> => {
      setIsLoadingProduto(true);
      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<ProdutoOptionProps>>
      >(
        formatQueryPagegTable(
          ConstanteEnderecoWebservice.PRODUTO_COR_TAMANHO_LISTAR_SELECT_ENTRADA_MERCADORIA,
          {
            ...dataPagination,
            orderColumn: 'Nome',
            orderDirection: 'asc',
          }
        ),
        {
          params: { nomeSkuCodigoExternoBarrasGtinEan: valorDigitado },
        }
      );

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        setTotalRegistros(response.dados.total || 0);

        if (response?.sucesso && response?.dados) {
          const data = response.dados?.registros.map((produtoCorTamanho) => ({
            label: produtoCorTamanho.nome,
            value: {
              ...produtoCorTamanho,
              coresOptions: [],
              tamanhosOptions: [],
            },
          }));

          setIsLoadingProduto(false);
          return data;
        }
      }

      setIsLoadingProduto(false);
      return [];
    },
    []
  );

  const validarPesquisa = useCallback(
    (pesquisa: string) => {
      const pesquisaVazia = pesquisa.trim() === '';
      if (pesquisaVazia) {
        return false;
      }
      const pesquisaPorCodigo =
        checarPesquisaPorCodigoBarras(pesquisa) ||
        checarPesquisaPorSKU(pesquisa) ||
        checarPesquisaPorGtinEan(pesquisa);

      if (!pesquisaPorCodigo) {
        formMethods.setValue('pesquisarPorLeitor', false);
      } else {
        formMethods.setValue('pesquisarPorLeitor', true);
      }

      return pesquisaPorCodigo;
    },
    [formMethods]
  );

  const buscarProduto = useCallback(
    async (
      valorPesquisado: string,
      dataPagination: PaginationData,
      pesquisarPorLeitor?: boolean
    ): Promise<OptionType<ProdutoOptionProps>[]> => {
      limparCampoProduto();
      if (pesquisarPorLeitor) {
        return buscarProdutoPorCodigo(valorPesquisado);
      }
      if (validarPesquisa(valorPesquisado)) {
        return buscarProdutoPorCodigo(valorPesquisado);
      }
      return buscarProdutoDigitando(valorPesquisado, dataPagination);
    },
    [
      buscarProdutoDigitando,
      buscarProdutoPorCodigo,
      limparCampoProduto,
      validarPesquisa,
    ]
  );

  const cadastrarOpcaoNova = useCallback(
    async (
      inputValue: string,
      tipoCadastro: TipoCadastro,
      temPermissao: boolean,
      produtoId: string
    ) => {
      if (temPermissao) {
        const modalResponse =
          tipoCadastro === 'cor'
            ? await ModalCadastrarCor({ inputValue, produtoId })
            : await ModalCadastrarTamanho({ inputValue, produtoId });

        let newOption: NewOption | undefined;

        if (tipoCadastro === 'cor' && 'cor' in modalResponse) {
          const { cor } = modalResponse;
          if (cor) {
            newOption = {
              id: cor.corId,
              descricao: cor.corDescricao,
            };
          }
        } else if (tipoCadastro === 'tamanho' && 'tamanho' in modalResponse) {
          const { tamanho } = modalResponse;
          if (tamanho) {
            newOption = {
              id: tamanho.id,
              descricao: tamanho.descricao,
            };
          }
        }

        if (newOption) {
          const option = {
            value: newOption.id,
            label: newOption.descricao,
          };
          return option;
        }
      }

      toast.warning(
        'Você não tem permissão para acessar essa função. Consulte o administrador da conta.'
      );

      return undefined;
    },
    []
  );
  async function handleCadastrarCor(inputValue: string) {
    const coresAtuais = produtoWatch?.value?.coresOptions || [];
    const novaCor = await cadastrarOpcaoNova(
      inputValue,
      'cor',
      temPermissaoCadastrarCor,
      produtoWatch?.value.id || ''
    );
    if (novaCor) {
      setValue('produto.value.coresOptions', [...coresAtuais, novaCor]);
    }
    return novaCor;
  }

  async function handleCadastrarTamanho(inputValue: string) {
    const tamanhosAtuais = tamanhosDoProduto;
    const novoTamanho = await cadastrarOpcaoNova(
      inputValue,
      'tamanho',
      temPermissaoCadastrarTamanho,
      produtoWatch?.value.id || ''
    );
    if (novoTamanho) {
      setValue('produto.value.tamanhosOptions', [
        ...tamanhosAtuais,
        novoTamanho,
      ]);
    }
    return novoTamanho;
  }

  const cadastrarNovoProduto = async (inputValue: string) => {
    const { id: idLojaAtual } = auth.getLoja();
    const data = formMethods.watch();
    const { produto: produtoCadastrado } = await ModalCadastrarProduto({
      inputValue,
      product: {
        descricaoProduto: inputValue,
        cfop: data.cfop,
        documentoFiscalItemId: produto.documentoFiscalItemId,
        quantidade: produto.quantidade,
        codigoCest: produto.codigoCest,
        codigoGTINEAN: produto.codigoGTINEAN,
        valorTotal: produto.valorTotal,
        valorUnitario: Number(produto.valorUnitario.toFixed(2)),
        ncm: produto.ncm,
      },
    });

    let precoCompra = 0;
    const precoCompraProduto = produtoCadastrado?.produtoPrecoLojas?.filter(
      (loja: { lojaId: string; precoCompra: number }) =>
        loja.lojaId === idLojaAtual
    );

    if (precoCompraProduto.length > 0) {
      const precoCompraLojaAtual = precoCompraProduto[0].precoCompra;
      precoCompra = Number(precoCompraLojaAtual);
    } else {
      precoCompra = 0;
    }
    const routeApi = `${ConstanteEnderecoWebservice.PRODUTO_COR_TAMANHO_OBTER_OPCAO_SELECT_ENTRADA_MERCADORIA}?produtoId=${produtoCadastrado.id}`;
    const response = await api.get<void, ResponseApi<ProdutoOptionProps>>(
      routeApi
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        const produtoTemVariacoes =
          response.dados.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;

        const newProduct = {
          label: response.dados.nome,
          value: {
            ...response.dados,
            coresOptions: [],
            tamanhosOptions: [],
          },
        };

        if (!produtoTemVariacoes) {
          setIsLoadingProduto(false);

          return {
            ...newProduct,
            value: {
              ...newProduct.value,
              precoCompra,
            },
          };
        }

        const [cores, tamanhos] = await Promise.all([
          buscarCoresDoProduto(response.dados.id),
          buscarTamanhoDoProduto(response.dados.id),
        ]);

        setIsLoadingProduto(false);

        return {
          ...newProduct,
          value: {
            ...newProduct.value,
            coresOptions: cores,
            tamanhosOptions: tamanhos,
            precoCompra,
          },
        };
      }
    }

    setIsLoadingProduto(false);
    return undefined;
  };

  const handleCadastrarProduto = async (inputValue: string) => {
    if (!temPermissaoCadastrarProduto) {
      toast.warning('Você não possui permissão para cadastrar produtos');
      return undefined;
    }
    const produto = await cadastrarNovoProduto(inputValue);
    await onChangeSelectProduto(produto, false);
    return produto;
  };

  return {
    obterItemVinculado,
    onChangeSelectProduto,
    handleCadastrarProduto,
    validarPesquisa,
    handleSubmit,
    buscarProduto,
    handleCadastrarCor,
    quantidadeMaiorQueZero,
    adicionarVariacaoNaLista,
    handleCadastrarTamanho,
    abriModalVariacoes,
    handleAbrirModalEscolherGradeTamanhos,
    setListaVariacoes,
    limparValoresFiscaisVoltarValorPrecoCompra,
    setItemEdicao,
    alterarVinculacao,
    listaVariacoes,
    isLoading,
    quantidadeTotal,
    quantidadeProdutoEscolhido,
    pesquisarPorLeitorWatch,
    isLoadingProduto,
    totalRegistros,
    produtoSelecionado,
    produtoDeVolumeUnitario,
    temGradeLancada,
    produtoTipoVariacao,
    produtoTemCores,
    coresDoProduto,
    corEscolhida,
    podeConfirmar,
    produtoTemTamanhos,
    tamanhosDoProduto,
    tamanhoEscolhido,
    itemEdicao,
    setValue,
  };
};
