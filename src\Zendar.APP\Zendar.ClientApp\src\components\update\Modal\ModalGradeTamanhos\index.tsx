import {
  <PERSON>dalProps,
  ModalContent,
  ModalBody,
  Icon,
  Flex,
  useMediaQuery,
  useDisclosure,
  ModalHeader,
  VStack,
  Text,
  ModalFooter,
  But<PERSON>,
  Stack,
} from '@chakra-ui/react';
import React, { useCallback, useState } from 'react';
import { useFieldArray, useForm, Control, FormProvider } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { NumberInput } from 'components/update/Input/NumberInput';

import { ErroAvisoIcon, SalvarInserirNovoIcon } from 'icons';

import { Totalizador } from './Totalizador';

interface ITamanho {
  produtoCorTamanhoId: string;
  tamanho: string;
  padraoSistema?: boolean;
  quantidade?: number;
  precoVenda?: number;
}

interface IFieldArrayType {
  tamanhos: ITamanho[];
}

type Tamanhos = {
  tamanhos: {
    produtoCorTamanhoId: string;
    tamanho: string;
    padraoSistema?: boolean | undefined;
    quantidade?: number | undefined;
  }[];
};

type ProdutoCorTamanho = {
  produtoNome: string;
  corDescricao?: string;
  tamanhoDescricao?: string;
  produtoCorTamanhoId: string;
  quantidade: number;
  precoVenda: number;
  adicionarNovamenteProduto?: boolean;
};

type ModalGradeTamanhosResponse = Array<ProdutoCorTamanho>;

type ModalGradeTamanhosProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> &
  InstanceProps<ModalGradeTamanhosResponse> & {
    produtoNome: string;
    corDescricao?: string;
    casasDecimaisQuantidade: number;
    volumeUnitario?: boolean;
    tamanhos: ITamanho[];
    inserirNovoTamanho?: boolean;
    padraoSistema?: boolean;
    limiteQuantidade?: number;
  };

const ModalGradeTamanhosComponent = ({
  produtoNome,
  corDescricao,
  onResolve,
  onReject,
  inserirNovoTamanho,
  casasDecimaisQuantidade,
  volumeUnitario,
  tamanhos,
  limiteQuantidade,
  ...rest
}: ModalGradeTamanhosProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm({ defaultValues: { tamanhos } });
  const { fields } = useFieldArray<IFieldArrayType, 'tamanhos'>({
    control: formMethods.control as Control<any>,
    name: 'tamanhos',
  });

  const tamanhosNoModal = formMethods?.watch('tamanhos');

  const quantidadeTotalInformadoNaGrade = (tamanhosNoModal || []).reduce(
    (acc: number, curr: any) => acc + (curr?.quantidade || 0),
    0
  );
  const quantidadeLimiteAtingida =
    (limiteQuantidade || 0) - quantidadeTotalInformadoNaGrade;
  const valorMaximoInput =
    quantidadeLimiteAtingida <= 0 ? 1 : quantidadeLimiteAtingida;

  const preencherCor = useCallback(
    (index: number) => {
      const quantidade = formMethods.watch(`tamanhos.${index}.quantidade`);
      if (quantidade && quantidade > 0) {
        return 'purple.50';
      }
      return 'white';
    },
    [formMethods]
  );

  const contemDescricaoTamanhoGrande = tamanhos.some(
    (tamanho) => tamanho.tamanho.length >= 5
  );

  const [isSmallerThan900] = useMediaQuery('(max-width: 900px)');
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

  const salvarDadosTamanhos = useCallback(
    (data: Tamanhos, isInserirNovoTamanho: boolean) => {
      setIsLoading(true);

      const responseData = data.tamanhos
        .filter((tamanho: ITamanho) => !!tamanho.quantidade)
        .map(
          (tamanho: ITamanho) =>
            ({
              produtoNome,
              corDescricao,
              precoVenda: tamanho.precoVenda,
              tamanhoDescricao: tamanho.padraoSistema ? '' : tamanho.tamanho,
              produtoCorTamanhoId: tamanho.produtoCorTamanhoId,
              quantidade: tamanho.quantidade,
              adicionarNovamenteProduto: isInserirNovoTamanho,
              padraoSistema: tamanho.padraoSistema,
            } as ProdutoCorTamanho)
        );

      onResolve(responseData);
      setIsLoading(false);
    },
    [corDescricao, onResolve, produtoNome]
  );

  const handleSubmit = formMethods.handleSubmit(async (data) => {
    salvarDadosTamanhos(data, false);
  });

  const handleInserirNovoTamanho = formMethods.handleSubmit(async (data) => {
    salvarDadosTamanhos(data, true);
  });

  const handleLimparDados = () => {
    fields.forEach((_, index) => {
      formMethods.setValue(`tamanhos.${index}.quantidade` as const, 0);
    });
  };

  return (
    <ModalPadraoChakra
      isCentered
      size={!isSmallerThan900 ? 'xl' : 'full'}
      {...rest}
      isOpen={isOpen}
      onClose={onClose}
    >
      <ModalContent
        marginBottom={{ base: 0, md: '3.75rem' }}
        marginTop={{ base: 0, md: '3.75rem' }}
        h="unset"
        maxW={{ base: '100%', md: inserirNovoTamanho ? '890px' : '690px' }}
        bg="gray.50"
      >
        <FormProvider {...formMethods}>
          {isLoading && <LoadingPadrao />}
          <ModalHeader
            mt={isSmallerThan900 ? 12 : undefined}
            mb={isSmallerThan900 ? 8 : undefined}
            px="0"
            justifyContent="space-between"
            mx={{ base: 6, md: 8 }}
            bg="gray.50"
          >
            <Flex justify="space-between">
              <VStack alignItems="flex-start" gap="2px" lineHeight="1">
                <Text color="primary.50" fontSize="16px">
                  {produtoNome}
                </Text>
                {corDescricao && (
                  <Text color="gray.700" fontSize="14px">
                    {`Cor: ${corDescricao}`}
                  </Text>
                )}
              </VStack>
              <Totalizador />
            </Flex>
          </ModalHeader>
          <ModalBody
            bg="gray.100"
            py={{ base: 4, md: 6 }}
            pl={{ base: 4, md: 6 }}
            pr="15px"
            sx={{
              '& .chakra-form-control': {
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',

                justifyContent: 'center',
              },
              '& .chakra-input__group': {
                width: volumeUnitario ? '55px' : 'full',
                '& > input': {
                  px: '1',
                },
              },
              '& .chakra-stack': {
                maxWidth: '55px',
                justifyContent: 'center',
                overflow: 'visible',
                '& .chakra-form__label': {
                  marginRight: '0',
                  whiteSpace: 'nowrap',
                },
              },
            }}
          >
            <Flex
              maxH="300px"
              px="10px"
              pb="10px"
              overflowX="hidden"
              overflowY="auto"
              flexDirection="column"
              h="full"
            >
              <SimpleGridForm
                columns={{
                  base: contemDescricaoTamanhoGrande || !volumeUnitario ? 2 : 4,
                  sm: contemDescricaoTamanhoGrande || !volumeUnitario ? 5 : 8,
                }}
              >
                {fields.map((field, index) => (
                  <>
                    <Flex flexDir="column" justify="center" align="center">
                      <Text fontSize="14px" fontWeight="bold">
                        {field.tamanho}
                      </Text>
                      <NumberInput
                        key={field.id}
                        id={`tamanho-${field.id}`}
                        name={`tamanhos.${index}.quantidade`}
                        min={0}
                        bg={preencherCor(index)}
                        borderColor="#BBBBBB"
                        _hover={{
                          borderColor: '#5502B2',
                        }}
                        borderRadius="5px"
                        variant="grade"
                        textAlign="center"
                        pr="0px"
                        max={
                          limiteQuantidade !== undefined
                            ? valorMaximoInput
                            : undefined
                        }
                        isDisabled={
                          limiteQuantidade !== undefined &&
                          (formMethods?.watch(`tamanhos.${index}.quantidade`) ||
                            0) === 0 &&
                          quantidadeLimiteAtingida <= 0
                        }
                        placeholder={
                          volumeUnitario
                            ? '0'
                            : `0,${'0'.repeat(casasDecimaisQuantidade)}`
                        }
                        scale={volumeUnitario ? 0 : casasDecimaisQuantidade}
                        autoFocus={index === 0}
                      />
                    </Flex>
                  </>
                ))}
              </SimpleGridForm>
            </Flex>
          </ModalBody>
          <ModalFooter
            justifyContent="center"
            mx={{ base: 6, md: 8 }}
            px="0"
            py="24px"
            bg="gray.50"
          >
            <Stack
              direction={{ base: 'column', md: 'row' }}
              w="full"
              gap="24px"
              justifyContent="center"
            >
              <Button
                borderRadius="full"
                variant="outlineDefault"
                colorScheme="gray"
                minW="150px"
                color="gray.500"
                fontSize="14px"
                borderColor="gray.500"
                height="32px"
                fontWeight="500"
                onClick={handleLimparDados}
                leftIcon={<ErroAvisoIcon />}
                isDisabled={isLoading}
              >
                Limpar dados
              </Button>
              <Button
                borderRadius="full"
                variant="outlineDefault"
                colorScheme="gray"
                fontSize="14px"
                height="32px"
                borderColor="gray.500"
                fontWeight="500"
                minW="96px"
                onClick={() => onReject(undefined)}
              >
                Cancelar
              </Button>
              {inserirNovoTamanho && (
                <Button
                  borderRadius="md"
                  variant="outline"
                  minW="150px"
                  color="gray.400"
                  fontSize="14px"
                  height="32px"
                  fontWeight="500"
                  leftIcon={<Icon as={SalvarInserirNovoIcon} fontSize="lg" />}
                  onClick={handleInserirNovoTamanho}
                  isDisabled={isLoading}
                  isLoading={isLoading}
                >
                  Confirmar e inserir novo
                </Button>
              )}
              <Button
                borderRadius="full"
                colorScheme="aquamarine"
                minW="160px"
                fontSize="14px"
                height="32px"
                fontWeight="500"
                onClick={handleSubmit}
                isDisabled={isLoading}
                isLoading={isLoading}
              >
                Confirmar
              </Button>
            </Stack>
          </ModalFooter>
        </FormProvider>
      </ModalContent>
    </ModalPadraoChakra>
  );
};

export const ModalGradeTamanhos = create<
  ModalGradeTamanhosProps,
  ModalGradeTamanhosResponse
>(ModalGradeTamanhosComponent);
