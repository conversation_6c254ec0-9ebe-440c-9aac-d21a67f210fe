import {
  Icon,
  Flex,
  Text,
  HStack,
  GridItem,
  IconButton,
} from '@chakra-ui/react';
import React from 'react';

import { MenuSelect } from 'pages/EntradaMercadoria/components/MenuSelect';

import CreatableSelect from 'components/PDV/Select/CreatableSelect';
import { chakraComponents } from 'components/PDV/Select/ReactSelectIntegracao';

import OptionType from 'types/optionType';

import { GradeTamanhosIcon } from 'icons';

export const Tamanho = ({
  temGradeLancada,
  tamanhosDoProduto,
  isLoadingProduto,
  produtoTemCores,
  produtoTemTamanhos,
  corEscolhida,
  handleCadastrarTamanho,
  handleAbrirModalEscolherGradeTamanhos,
  abrirModalVariacoes,
  mensagemGradeEmUso = 'Tamanhos informados na grade',
  placeholder = 'Informe o tamanho',
  onChangeSelect,
}: {
  temGradeLancada: boolean;
  tamanhosDoProduto: { value: string; label: string }[];
  isLoadingProduto: boolean;
  produtoTemCores: boolean;
  produtoTemTamanhos: boolean;
  corEscolhida: {
    label: string;
    value: any;
  } | null;
  handleCadastrarTamanho: (tamanho: string) => void;
  handleAbrirModalEscolherGradeTamanhos: () => void;
  abrirModalVariacoes?: () => void;
  mensagemGradeEmUso?: string;
  placeholder?: string;
  onChangeSelect?: (option: OptionType<any> | null | undefined) => void;
}) => {
  return (
    <GridItem
      mt={temGradeLancada ? '18px' : undefined}
      colSpan={[12, 12, 4, 4]}
    >
      <HStack alignItems="stretch">
        {temGradeLancada ? (
          <Flex
            alignItems="center"
            justifyContent="flex-start"
            bg="gray.200"
            border="1px"
            borderColor="gray.300"
            w="full"
            pl="12px"
            h="36px"
            borderRadius="md"
            userSelect="none"
          >
            <Text color="gray.700" fontSize="sm" textAlign="left">
              {mensagemGradeEmUso}
            </Text>
          </Flex>
        ) : (
          <CreatableSelect
            id="tamanho"
            name="listaTamanhoIdQuantidade.0.tamanho"
            label="Tamanho"
            placeholder={placeholder}
            creatableInputTextPreffix="Cadastrar o tamanho"
            handleCreateOption={handleCadastrarTamanho}
            menuPortalTarget={document?.body}
            options={tamanhosDoProduto}
            disabled={
              isLoadingProduto || produtoTemCores ? !corEscolhida : false
            }
            onChangeSelect={onChangeSelect}
            asControlledByObject
            components={{
              ...chakraComponents,
              MenuList: (props: any) => (
                <MenuSelect
                  texto={
                    tamanhosDoProduto?.length > 0
                      ? '**Exibindo apenas tamanhos ativos do produto**'
                      : ''
                  }
                  {...props}
                />
              ),
            }}
            required
            actionLinkText={
              (isLoadingProduto || produtoTemCores ? !corEscolhida : false)
                ? ''
                : 'Adicionar novo'
            }
            actionLinkOnClick={abrirModalVariacoes}
          />
        )}

        <Flex ml="23px" flex="1" alignItems="flex-end">
          <IconButton
            aria-label="Selecionar grade de tamanhos"
            icon={<Icon as={GradeTamanhosIcon} fontSize="xl" />}
            colorScheme="whiteAlpha"
            color="gray.800"
            bg={temGradeLancada ? 'secondary.300' : 'white'}
            borderRadius="md"
            _hover={{
              bg: temGradeLancada ? 'secondary.200' : 'gray.100',
            }}
            _active={{
              bg: temGradeLancada ? 'secondary.200' : 'gray.100',
            }}
            _focus={{
              bg: temGradeLancada ? 'secondary.300' : 'white',
              border: '2px',
              borderColor: '#390073',
              borderStyle: 'solid',
            }}
            boxShadow="0px 0px 4px #0000003E"
            border="1px"
            borderColor="gray.200"
            onClick={handleAbrirModalEscolherGradeTamanhos}
            isDisabled={
              !produtoTemTamanhos || produtoTemCores ? !corEscolhida : false
            }
          />
        </Flex>
      </HStack>
    </GridItem>
  );
};
