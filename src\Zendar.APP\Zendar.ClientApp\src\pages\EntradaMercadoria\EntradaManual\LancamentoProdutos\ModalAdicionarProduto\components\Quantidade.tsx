import { Flex, Text, GridItem } from '@chakra-ui/react';

import CampoContainer from 'components/PDV/Geral/CampoContainer';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { NumberInput } from 'components/update/Input/NumberInput';

type QuantidadeProps = {
  produtoTemGradeLancada: boolean;
  casasDecimaisQuantidade: number;
  produtoDeVolumeUnitario: boolean;
  produtoPossuiVariacoes: boolean;
  quantidade: number;
  manterTamanho?: boolean;
  quantidadeProdutoParaVincular?: number;
};

export const Quantidade = ({
  produtoTemGradeLancada,
  casasDecimaisQuantidade,
  produtoDeVolumeUnitario,
  produtoPossuiVariacoes,
  quantidade,
  manterTamanho,
  quantidadeProdutoParaVincular,
}: QuantidadeProps) => {
  const scale = produtoDeVolumeUnitario
    ? 0
    : Math.max(0, casasDecimaisQuantidade);

  const placeholder = scale === 0 ? '0' : `0,${'0'.repeat(scale)}`;

  const colSpanGradeLancada = manterTamanho
    ? 2
    : [12, produtoPossuiVariacoes ? 2 : 4, produtoPossuiVariacoes ? 2 : 4, 2];

  const colSpanSemGrade = manterTamanho
    ? produtoPossuiVariacoes
      ? 2
      : 4
    : [
        12,
        12,
        produtoPossuiVariacoes ? 4 : 12,
        produtoPossuiVariacoes ? 4 : 12,
      ];

  const baseNumberInputProps = {
    id: 'listaTamanhoIdQuantidade.0.quantidade',
    name: 'listaTamanhoIdQuantidade.0.quantidade',
    label: 'Quantidade',
    placeholder,
    scale,
    isDisabled: produtoTemGradeLancada,
    pl: '12px',
    textAlign: 'left' as const,
  };

  if (produtoTemGradeLancada) {
    return (
      <GridItem colSpan={colSpanGradeLancada}>
        <CampoContainer
          id="quantidade"
          name="quantidade"
          label="Quantidade"
          display="flex"
          flexDirection="column"
        >
          <Flex
            alignItems="center"
            justifyContent="flex-start"
            bg="gray.200"
            border="1px"
            borderColor="gray.300"
            w="full"
            pl="12px"
            h="36px"
            borderRadius="md"
            userSelect="none"
          >
            <Text color="gray.700" fontSize="sm" textAlign="left">
              {quantidade}
            </Text>
          </Flex>
        </CampoContainer>
      </GridItem>
    );
  }

  return (
    <GridItem colSpan={colSpanSemGrade}>
      {manterTamanho ? (
        <NumberInput
          {...baseNumberInputProps}
          max={quantidadeProdutoParaVincular}
          colSpan={12}
        />
      ) : (
        <SimpleGridForm>
          <NumberInput
            {...baseNumberInputProps}
            colSpan={[
              12,
              12,
              produtoPossuiVariacoes ? 6 : 4,
              produtoPossuiVariacoes ? 6 : 4,
            ]}
          />
        </SimpleGridForm>
      )}
    </GridItem>
  );
};
