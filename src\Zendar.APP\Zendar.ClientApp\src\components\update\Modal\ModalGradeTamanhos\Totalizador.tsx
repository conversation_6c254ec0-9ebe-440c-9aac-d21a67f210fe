import { HStack, Text } from '@chakra-ui/react';
import React, { memo } from 'react';
import { useFormContext } from 'react-hook-form';

export const Totalizador = memo(() => {
  const { watch } = useFormContext();

  const tamanhos = watch('tamanhos');

  const quantidadeTotal = (tamanhos || []).reduce(
    (acc: number, curr: any) => acc + (curr?.quantidade || 0),
    0
  );

  return (
    <HStack spacing={1} whiteSpace="nowrap" align="flex-end">
      <Text lineHeight="none" fontSize="14px" color="gray.700" fontWeight="400">
        Total de produtos:
      </Text>
      <Text lineHeight="none" fontSize="14px" fontWeight="bold">
        {quantidadeTotal}
      </Text>
    </HStack>
  );
});
