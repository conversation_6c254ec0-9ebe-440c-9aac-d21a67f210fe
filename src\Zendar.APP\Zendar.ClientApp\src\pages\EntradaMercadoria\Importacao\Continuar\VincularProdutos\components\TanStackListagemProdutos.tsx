import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Icon,
  HStack,
  Text,
  Flex,
  useToken,
  Spinner,
  Center,
} from '@chakra-ui/react';
import {
  useReactTable,
  getCoreRowModel,
  getExpandedRowModel,
  ColumnDef,
  flexRender,
  Row,
  ExpandedState,
} from '@tanstack/react-table';
import React, { useMemo, useState, useRef, useCallback } from 'react';
import { FiCheckCircle, FiChevronUp, FiChevronDown } from 'react-icons/fi';

import { DecimalMask } from 'helpers/format/fieldsMasks';
import useWindowSize from 'helpers/layout/useWindowSize';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import { LoadMoreRowsParams } from 'components/update/Table/VirtualizedInfiniteTable';

import {
  Produto,
  EntradaMercadoriaStatusVinculoProduto,
  InformacoesRodape,
} from '../hooks/useProdutosVinculacao';
import { TextoTooltip } from '../TextoTooltip';

import { DetalhesItemProduto } from './DetalhesItemProduto';

interface TanStackListagemProdutosProps {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  isLoading: boolean;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditar: (index: number) => Promise<void>;
  handleVincularProduto: (
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) => Promise<void>;
  loadMoreRows: (params: LoadMoreRowsParams) => Promise<void>;
}

export function TanStackListagemProdutos({
  produtos,
  informacoesRodape,
  isLoading,
  handleToggleLinhaProduto,
  handleEditar,
  handleVincularProduto,
  loadMoreRows,
}: TanStackListagemProdutosProps) {
  const [teal600, aquamarine100] = useToken('colors', [
    'teal.600',
    'aquamarine.100',
  ]);
  const { casasDecimais } = usePadronizacaoContext();
  const { height: windowHeight } = useWindowSize();
  const [expanded, setExpanded] = useState<ExpandedState>({});

  const obterCorBackground = (
    status: EntradaMercadoriaStatusVinculoProduto
  ) => {
    const enumStatus = EntradaMercadoriaStatusVinculoProduto;
    const vinculado = status === enumStatus.VINCULADO;
    const naoVinculado = status === enumStatus.NAO_VINCULADO;

    if (vinculado) return `${teal600} !important`;
    if (naoVinculado) return 'white';
    return `${aquamarine100} !important`;
  };

  const handleVincularClick = (produto: Produto, index: number) => {
    const produtoNaoEstaVinculado =
      produto.statusVinculo ===
      EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

    if (produtoNaoEstaVinculado) {
      handleVincularProduto(index);
      return;
    }

    handleVincularProduto(index, {
      id: produto.produtoVinculado?.id || '',
      nome: produto.produtoVinculado?.nome || '',
      tipoProduto: produto.produtoVinculado?.tipoProduto || 2,
      volumeUnitario: produto.produtoVinculado?.volumeUnitario || false,
      referencia: produto.produtoVinculado?.referencia || '',
      precoCompra: produto.produtoVinculado?.precoCompra || 0,
      coresOptions: [],
      tamanhosOptions: [],
    });
  };

  const columns = useMemo<ColumnDef<Produto>[]>(
    () => [
      {
        id: 'expander',
        header: '',
        cell: ({ row }) => {
          const produto = row.original;
          const statusQuePodemMostrarDetalhes = [
            EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES,
          ];
          const podeMostrarDetalhes =
            statusQuePodemMostrarDetalhes.includes(produto.statusVinculo) ||
            !!produto.dadosAdicionais;

          return podeMostrarDetalhes ? (
            <Button
              bg="transparent"
              p="4px"
              h="fit-content"
              borderRadius="6px"
              minW="16px"
              onClick={() => {
                row.toggleExpanded();
                handleToggleLinhaProduto(row.index);
              }}
            >
              <Icon
                as={row.getIsExpanded() ? FiChevronUp : FiChevronDown}
                transition="all 0.3s"
              />
            </Button>
          ) : null;
        },
        size: 50,
      },
      {
        accessorKey: 'descricaoProdutoNota',
        header: 'Produto',
        cell: ({ getValue, row }) => {
          const produto = row.original;
          return (
            <Box>
              <Text fontSize="14px">{getValue() as string}</Text>
              {row.getIsExpanded() && produto.dadosAdicionais && (
                <Box mt="4px" fontSize="12px" fontWeight="bold">
                  <TextoTooltip
                    texto={produto.dadosAdicionais}
                    maxWidth="100%"
                  />
                </Box>
              )}
            </Box>
          );
        },
        minSize: 300,
      },
      {
        accessorKey: 'quantidade',
        header: 'Quantidade',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="center">
            {DecimalMask(
              getValue() as number,
              casasDecimais.casasDecimaisQuantidade
            )}
          </Text>
        ),
        size: 120,
      },
      {
        accessorKey: 'valorUnitario',
        header: 'Valor unitário',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="right">
            {DecimalMask(
              getValue() as number,
              casasDecimais.casasDecimaisValor
            )}
          </Text>
        ),
        size: 140,
      },
      {
        accessorKey: 'valorTotal',
        header: 'Valor total',
        cell: ({ getValue }) => (
          <Text fontSize="14px" textAlign="right">
            {DecimalMask(getValue() as number, 2, 2)}
          </Text>
        ),
        size: 140,
      },
      {
        id: 'acoes',
        header: 'Ações',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const produtoNaoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

          return produtoEstaVinculado ? (
            <Flex justifyContent="space-between">
              <HStack spacing="1" color="secondary.300">
                <Icon as={FiCheckCircle} boxSize="4" />
                <Text fontSize="xs">Vinculado</Text>
              </HStack>
              <ActionsMenu
                colorScheme="white"
                backgroundHoverColor="gray.500"
                items={[
                  {
                    content: 'Editar',
                    onClick: () => handleEditar(row.index),
                  },
                ]}
              />
            </Flex>
          ) : (
            <Flex alignItems="center" justifyContent="center">
              <Button
                size="xs"
                colorScheme="orange"
                minW="136px"
                onClick={() => handleVincularClick(produto, row.index)}
              >
                {produtoNaoEstaVinculado
                  ? 'Vincular ao sistema'
                  : 'Informar variações'}
              </Button>
            </Flex>
          );
        },
        size: 180,
      },
    ],
    [casasDecimais, handleEditar, handleVincularClick, handleToggleLinhaProduto]
  );

  const table = useReactTable({
    data: produtos,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    onExpandedChange: setExpanded,
    state: {
      expanded,
    },
    getRowId: (row, index) => `produto-${index}`,
  });

  const maxContainerHeight = useMemo((): string => {
    const stepDescriptionHeight = 80;
    const containerPadding = 48;
    const totalizadoresHeight = informacoesRodape.totalProdutos > 0 ? 145 : 0;
    const footerHeight = 70;
    const marginBottom = 24;

    const availableHeight =
      windowHeight -
      stepDescriptionHeight -
      containerPadding -
      totalizadoresHeight -
      footerHeight -
      marginBottom;

    const dynamicHeight = Math.max(availableHeight, 300);
    return `${dynamicHeight}px`;
  }, [windowHeight, informacoesRodape.totalProdutos]);

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      borderRadius="md"
      border="1px"
      bg="gray.50"
      borderColor="gray.200"
      maxH={maxContainerHeight}
      py={{ base: 4, sm: 6, md: 6 }}
      pl={{ base: 4, sm: 6, md: 6 }}
      pr={{ base: '6px', sm: '14px', md: '24px' }}
      width="100%"
      overflowX="auto"
    >
      <Table
        variant="simple"
        size="sm"
        bg="gray.50"
        minWidth="880px"
        sx={{
          tableLayout: 'fixed',
          '& thead > tr > th': {
            bg: 'gray.50',
            border: 'none',
            whiteSpace: 'nowrap',
            fontSize: '12px',
            fontWeight: 'bold',
            color: 'gray.600',
          },
          '& tbody > tr': {
            borderRadius: 'md',
            boxShadow: '0px 0px 2px #00000029',
            ...(informacoesRodape.totalProdutos > 0
              ? { border: '1px', borderColor: 'gray.100' }
              : {}),
          },
          '& tbody > tr > td': {
            bg: 'white',
            lineHeight: 'none',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          },
        }}
      >
        <Thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Th
                  key={header.id}
                  width={header.getSize()}
                  minWidth={header.column.columnDef.minSize}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </Th>
              ))}
            </Tr>
          ))}
        </Thead>
        <Tbody>
          {table.getRowModel().rows.map((row) => {
            const produto = row.original;
            return (
              <React.Fragment key={row.id}>
                <Tr
                  bg={obterCorBackground(produto.statusVinculo)}
                  sx={{
                    '& > td': {
                      bg: obterCorBackground(produto.statusVinculo),
                      color:
                        produto.statusVinculo ===
                        EntradaMercadoriaStatusVinculoProduto.VINCULADO
                          ? 'white'
                          : 'inherit',
                    },
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <Td
                      key={cell.id}
                      width={cell.column.getSize()}
                      minWidth={cell.column.columnDef.minSize}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </Td>
                  ))}
                </Tr>
                {row.getIsExpanded() && (
                  <Tr>
                    <Td colSpan={columns.length} p={0}>
                      <DetalhesItemProduto
                        produto={produto}
                        obterCorBackground={obterCorBackground}
                      />
                    </Td>
                  </Tr>
                )}
              </React.Fragment>
            );
          })}
        </Tbody>
      </Table>

      {isLoading && (
        <Center py={8}>
          <Spinner size="lg" color="teal.500" />
        </Center>
      )}

      {produtos.length === 0 && !isLoading && (
        <Box textAlign="center" py={8}>
          <Text color="gray.500">Nenhum produto adicionado.</Text>
        </Box>
      )}
    </Box>
  );
}
