import { Icon, Flex, Text, Box, GridItem } from '@chakra-ui/react';
import { Dispatch, SetStateAction } from 'react';
import { FiCheck } from 'react-icons/fi';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { LixeiraIcon } from 'icons';

interface EntradaMercadoria {
  quantidadeEntrada: number;
  valorUnitarioEntrada: number;
  cfopEntrada: string;
  itens: {
    produto: {
      id: string;
      descricao: string | null;
    };
    cor: {
      id: string;
      descricao: string | null;
    };
    tamanho: {
      id: string;
      descricao: string | null;
    };
    quantidade: number;
    valorUnitario: number;
    custoAdicional: number;
  }[];
}

export const ListagemItemEdicao = ({
  itens,
  casasDecimaisQuantidade,
  removerItemEditado,
}: {
  itens: EntradaMercadoria['itens'];
  casasDecimaisQuantidade: number;
  removerItemEditado: Dispatch<
    SetStateAction<EntradaMercadoria['itens'] | null>
  >;
}) => {
  const quantidadeTotal = itens.reduce((acc, curr) => acc + curr.quantidade, 0);

  return (
    <GridItem colSpan={12} maxW="full">
      {itens?.length > 0 && (
        <Box>
          <Flex flexDirection="column" bg="white" borderRadius="5px">
            <Flex
              gap="10px"
              px="24px"
              mx="24px"
              height="34px"
              w="calc(100% - 48px)"
              pt="16px"
            >
              <Text
                fontSize="10px"
                color="gray.500"
                fontWeight="500"
                minW={['30%', '50%']}
              >
                Produto
              </Text>
              <Text
                fontSize="10px"
                color="gray.500"
                fontWeight="500"
                minW={['30%', '20%']}
              >
                Cor
              </Text>
              <Text
                fontSize="10px"
                color="gray.500"
                fontWeight="500"
                minW={['30%', '15%']}
              >
                Tamanho
              </Text>
              <Text
                fontSize="10px"
                color="gray.500"
                minW={['30%', '10%']}
                fontWeight="500"
              >
                Quantidade
              </Text>
              <Text
                fontSize="10px"
                color="gray.500"
                fontWeight="500"
                minW="16px"
                aria-label="Ações"
              />
            </Flex>
            {itens?.map((variacao) => {
              const removerProduto = () => {
                removerItemEditado(null);
              };
              return (
                <Flex flexDir="column">
                  <Flex
                    gap="10px"
                    px="24px"
                    w="calc(100% - 48px)"
                    mx="24px"
                    borderTop="1px"
                    borderColor="gray.100"
                    height="54px"
                    align="center"
                  >
                    <Text
                      fontSize="14px"
                      color="primary.50"
                      minW={['30%', '50%']}
                      fontWeight="600"
                    >
                      {variacao?.produto?.descricao || '-----'}
                    </Text>
                    <Text
                      fontSize="14px"
                      color="gray.700"
                      fontWeight="bold"
                      minW={['30%', '20%']}
                    >
                      {variacao?.cor?.descricao || '-----'}
                    </Text>
                    <Text
                      fontSize="14px"
                      color="gray.700"
                      fontWeight="bold"
                      minW={['30%', '15%']}
                    >
                      {variacao?.tamanho?.descricao || '-----'}
                    </Text>
                    <Text
                      fontSize="14px"
                      color="gray.700"
                      fontWeight="bold"
                      minW={['30%', '10%']}
                    >
                      {DecimalMask(
                        variacao.quantidade,
                        casasDecimaisQuantidade
                      )}
                    </Text>
                    <Box>
                      <Flex justify="flex-end">
                        <Icon
                          fontSize="16px"
                          cursor="pointer"
                          as={LixeiraIcon}
                          _hover={{
                            color: 'red.500',
                          }}
                          tabIndex={0}
                          _active={{
                            color: 'red.500',
                          }}
                          onClick={() => {
                            removerProduto();
                          }}
                        />
                      </Flex>
                    </Box>
                  </Flex>
                </Flex>
              );
            })}
            <Flex
              gap="10px"
              px="24px"
              w="calc(100% - 68px)"
              mx="24px"
              borderTop="1px"
              borderColor="gray.100"
              height="54px"
              justify="flex-end"
              align="center"
            >
              <Text textAlign="end" fontSize="14px" color="gray.700">
                Quantidade total:
              </Text>
              <Flex
                w="120px"
                h="24px"
                pl="18px"
                lineHeight="1.7"
                borderRadius="16px"
                align="center"
                pr="12px"
                color="black"
                bg="secondary.400"
              >
                <Text fontSize="14px" fontWeight="bold">
                  {DecimalMask(quantidadeTotal, 2)}
                </Text>
                <Icon as={FiCheck} fontSize="16px" margin="0 0 0 auto" />
              </Flex>
            </Flex>
          </Flex>
        </Box>
      )}
    </GridItem>
  );
};
