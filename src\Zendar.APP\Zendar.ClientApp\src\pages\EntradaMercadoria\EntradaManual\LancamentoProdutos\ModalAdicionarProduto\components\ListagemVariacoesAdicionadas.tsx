import { Icon, Flex, Text, Box, GridItem } from '@chakra-ui/react';
import React from 'react';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { LixeiraIcon } from 'icons';

import { EntradaMercadoriaAdicionarItens } from '../validationForm';

export const ListagemVariacoesAdicionadas = ({
  listaVariacoes,
  setListaVariacoes,
  casasDecimaisQuantidade,
  produtoTemCores,
  produtoTemTamanhos,
  limparValoresFiscaisVoltarValorPrecoCompra,
}: {
  listaVariacoes: EntradaMercadoriaAdicionarItens[];
  setListaVariacoes: React.Dispatch<
    React.SetStateAction<EntradaMercadoriaAdicionarItens[]>
  >;
  casasDecimaisQuantidade: number;
  produtoTemCores: boolean;
  produtoTemTamanhos: boolean;
  limparValoresFiscaisVoltarValorPrecoCompra: () => void;
}) => {
  return (
    <GridItem colSpan={12} maxW="full">
      {listaVariacoes.length > 0 && (
        <Box>
          <Flex flexDirection="column" bg="white" borderRadius="5px">
            <Flex
              gap="10px"
              px="24px"
              mx="24px"
              height="34px"
              w="calc(100% - 48px)"
              pt="16px"
            >
              <Text
                fontSize="10px"
                color="gray.500"
                fontWeight="500"
                minW={['30%', '54%']}
              >
                Variação
              </Text>
              <Text
                fontSize="10px"
                color="gray.500"
                minW={['30%', '45%']}
                fontWeight="500"
              >
                Quantidade
              </Text>
              <Text
                fontSize="10px"
                color="gray.500"
                fontWeight="500"
                minW="16px"
                aria-label="Ações"
              />
            </Flex>
            {listaVariacoes.map((variacao) => {
              return (
                <Flex flexDir="column">
                  {variacao.listaTamanhoIdQuantidade.map(
                    (tamanhoIdQuantidade) => {
                      const variacaoTemTamanhoCor =
                        produtoTemCores && produtoTemTamanhos;
                      const variacaoTemTamanhoApenas =
                        produtoTemTamanhos && !produtoTemCores;
                      const variacaoTemCorApenas =
                        produtoTemCores && !produtoTemTamanhos;

                      const filtrarCor = (idCor: string) => {
                        setListaVariacoes((prev) => {
                          const variacaoParaExcluir = prev.find(
                            (variacaoFiltrada) =>
                              variacaoFiltrada.corId === idCor
                          );
                          if (variacaoParaExcluir) {
                            return prev.filter(
                              (variacaoFiltrada) =>
                                variacaoFiltrada.corId !== idCor
                            );
                          }
                          return prev;
                        });
                      };

                      const filtrarTamanho = (idTamanho: string) => {
                        setListaVariacoes((prev) => {
                          const variacaoParaExcluir = prev.find(
                            (variacaoFiltrada) =>
                              variacaoFiltrada.listaTamanhoIdQuantidade[0]
                                .id === idTamanho
                          );
                          if (variacaoParaExcluir) {
                            return prev.filter(
                              (variacaoFiltrada) =>
                                variacaoFiltrada.listaTamanhoIdQuantidade[0]
                                  .id !== idTamanho
                            );
                          }
                          return prev;
                        });
                      };

                      const filtrarTamanhoDaCor = (
                        idCor: string,
                        idTamanho: string
                      ) => {
                        setListaVariacoes((prev) => {
                          return prev
                            .map((variacaoFiltrada) => {
                              if (variacaoFiltrada.corId === idCor) {
                                const possuiSoEsseTamanho =
                                  variacaoFiltrada?.listaTamanhoIdQuantidade
                                    ?.length === 1;

                                if (possuiSoEsseTamanho) {
                                  return undefined;
                                }

                                return {
                                  ...variacaoFiltrada,
                                  listaTamanhoIdQuantidade:
                                    variacaoFiltrada.listaTamanhoIdQuantidade.filter(
                                      (item) => item.id !== idTamanho
                                    ),
                                };
                              }

                              return variacaoFiltrada;
                            })
                            .filter(
                              (
                                itemVariacao
                              ): itemVariacao is EntradaMercadoriaAdicionarItens =>
                                !!itemVariacao
                            );
                        });
                      };

                      const nomeVariacao = variacaoTemTamanhoCor
                        ? `${variacao.corDescricao} - ${tamanhoIdQuantidade.tamanhoDescricao}`
                        : produtoTemCores
                        ? `${variacao.corDescricao}`
                        : `${tamanhoIdQuantidade.tamanhoDescricao}`;
                      return (
                        <Flex
                          gap="10px"
                          px="24px"
                          w="calc(100% - 48px)"
                          mx="24px"
                          borderTop="1px"
                          borderColor="gray.100"
                          height="54px"
                          align="center"
                        >
                          <Text
                            fontSize="14px"
                            color="gray.700"
                            minW={['30%', '54%']}
                          >
                            {nomeVariacao}
                          </Text>
                          <Text
                            fontSize="14px"
                            color="gray.700"
                            fontWeight="bold"
                            minW={['30%', '45%']}
                          >
                            {DecimalMask(
                              tamanhoIdQuantidade.quantidade,
                              casasDecimaisQuantidade
                            )}
                          </Text>
                          <Box>
                            <Flex justify="flex-end">
                              <Icon
                                fontSize="16px"
                                cursor="pointer"
                                as={LixeiraIcon}
                                _hover={{
                                  color: 'red.500',
                                }}
                                tabIndex={0}
                                _active={{
                                  color: 'red.500',
                                }}
                                onClick={() => {
                                  if (variacaoTemCorApenas) {
                                    filtrarCor(variacao.corId);
                                  } else if (variacaoTemTamanhoApenas) {
                                    filtrarTamanho(tamanhoIdQuantidade.id);
                                  } else {
                                    filtrarTamanhoDaCor(
                                      variacao.corId,
                                      tamanhoIdQuantidade.id
                                    );
                                  }

                                  if (listaVariacoes?.length === 1) {
                                    limparValoresFiscaisVoltarValorPrecoCompra();
                                  }
                                }}
                              />
                            </Flex>
                          </Box>
                        </Flex>
                      );
                    }
                  )}
                </Flex>
              );
            })}
            <Flex
              gap="10px"
              px="24px"
              w="calc(100% - 48px)"
              mx="24px"
              borderTop="1px"
              borderColor="gray.100"
              height="54px"
              align="center"
            >
              <Text
                minW={['30%', '54%']}
                textAlign="end"
                fontSize="14px"
                color="gray.700"
              >
                Quantidade total:
              </Text>
              <Text
                minW={['30%', '45%']}
                fontSize="14px"
                fontWeight="bold"
                color="black"
              >
                {DecimalMask(
                  listaVariacoes.reduce(
                    (acc, curr) =>
                      acc +
                      curr.listaTamanhoIdQuantidade.reduce(
                        (acc2, curr2) => acc2 + curr2.quantidade,
                        0
                      ),
                    0
                  ),
                  2
                )}
              </Text>
              <Text minW="16px" />
            </Flex>
          </Flex>
        </Box>
      )}
    </GridItem>
  );
};
